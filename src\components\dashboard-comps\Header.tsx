"use client";
import React, { useState } from "react";
import { usePathname } from "next/navigation";
import Image from "next/image";
import { MdOutlineMarkEmailUnread } from "react-icons/md";
import { IoMenu } from "react-icons/io5";
import { HeaderProps } from "@/types/dashboardTypes";
import { navGroups } from "@/lib/navItems";
import SecurityAlertModal from "@/components/SecurityAlertModal";

export default function Header({ onMenuClick, isSidebarOpen }: HeaderProps) {
  const pathname = usePathname();
  const [isModalOpen, setIsModalOpen] = useState(false);

  const activeNavItem = navGroups
    .flatMap((group) => group.items)
    .find((item) => item.href === pathname);
  const activeNav = activeNavItem?.label || "Dashboard";

  const handleEmailClick = () => {
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  return (
    <>
      <div className="flex flex-col sm:flex-row sm:items-center justify-between space-y-4 sm:space-y-0">
        <div className="flex items-center justify-between w-full">
          <button
            className="lg:hidden text-white mr-2 sm:mr-4"
            onClick={onMenuClick}
            aria-label={isSidebarOpen ? "Close menu" : "Open menu"}
            onKeyDown={(e) => e.key === "Enter" && onMenuClick()}
          >
            <IoMenu size={24} />
          </button>
          <h1 className="text-xl sm:text-2xl font-medium text-white mx-auto lg:mx-0">
            {activeNav}
          </h1>
          <div className="flex items-center space-x-2 sm:space-x-4 lg:space-x-8 lg:justify-end">
            <div 
              className="p-2 sm:p-3.5 border cursor-pointer bg-[#1F1F1F] text-white border-solid gradient-border hover:bg-[#2F2F2F] transition-colors"
              onClick={handleEmailClick}
              role="button"
              tabIndex={0}
              onKeyDown={(e) => {
                if (e.key === "Enter" || e.key === " ") {
                  e.preventDefault();
                  handleEmailClick();
                }
              }}
              aria-label="View security alerts"
            >
              <MdOutlineMarkEmailUnread size={20} />
            </div>
            <div className="flex items-center cursor-pointer">
              <div className="p-3 sm:p-4.5 bg-[#3D3D3D] rounded-md">
                <Image
                  src="/user.svg"
                  alt="User Profile"
                  width={14}
                  height={14}
                  className="lg:hidden"
                />
                <Image
                  src="/user.svg"
                  alt="User Profile"
                  width={14}
                  height={14}
                  className="hidden lg:block"
                />
              </div>
              <p className="py-2 px-4 sm:px-9 border-y border-r bg-[#1F1F1F] text-white border-solid gradient-border-x hidden lg:block">
                My Profile
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Security Alert Modal */}
      <SecurityAlertModal 
        isOpen={isModalOpen} 
        onClose={handleCloseModal} 
      />
    </>
  );
}