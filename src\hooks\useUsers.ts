import { useState, useEffect, useCallback, useRef } from "react";
import { User } from "@/types/userTypes";
import { userService, ApiTeamMember, InviteUserData, ApiInvitation } from "@/utils/userServices";
import toast from "react-hot-toast";
import { showDetailedErrorToast } from "@/components/ErrorToast";
import Cookies from "js-cookie";
import { useRoleAccess } from "@/hooks/useRoleAccess";

interface ApiError {
  response?: {
    status?: number;
    data?: {
      message?: string;
      errors?: string[];
      error?: string;
    };
  };
  message?: string;
}

// Helper functions for managing invited user names in cookies
const getInvitedNamesFromCookie = (): Record<string, string> => {
  try {
    const stored = Cookies.get("invitedUserNames");
    return stored ? JSON.parse(stored) : {};
  } catch {
    return {};
  }
};

const storeInvitedNameInCookie = (email: string, name: string) => {
  try {
    const existingNames = getInvitedNamesFromCookie();
    existingNames[email] = name;
    Cookies.set("invitedUserNames", JSON.stringify(existingNames), {
      expires: 7, // 7 days expiry
      secure: process.env.NODE_ENV === "production",
      sameSite: "strict",
    });
    console.log("Stored invited user name in cookie:", { email, name });
  } catch (error) {
    console.warn("Failed to store invited user name in cookie:", error);
  }
};

const removeInvitedNameFromCookie = (email: string) => {
  try {
    const existingNames = getInvitedNamesFromCookie();
    delete existingNames[email];
    if (Object.keys(existingNames).length === 0) {
      Cookies.remove("invitedUserNames");
    } else {
      Cookies.set("invitedUserNames", JSON.stringify(existingNames), {
        expires: 7,
        secure: process.env.NODE_ENV === "production",
        sameSite: "strict",
      });
    }
    console.log("Removed invited user name from cookie:", email);
  } catch (error) {
    console.warn("Failed to remove invited user name from cookie:", error);
  }
};

// Transform API data to UI format
const transformApiTeamMember = (apiMember: ApiTeamMember): User => {
  // Check if we have a stored name for this user (from invitation)
  const storedNames = getInvitedNamesFromCookie();
  const originalName = storedNames[apiMember.email];

  // Use original name if available, otherwise use backend name
  const displayName = originalName || apiMember.name;

  // Clean up stored name when user becomes active (invitation accepted)
  if (originalName) {
    removeInvitedNameFromCookie(apiMember.email);
    console.log(`Using original invited name "${originalName}" instead of backend name "${apiMember.name}"`);
  }

  return {
    id: apiMember.id || apiMember._id || `temp-${Date.now()}-${Math.random()}`,
    name: displayName,
    email: apiMember.email,
    role: apiMember.role === "admin" ? "Admin" :
          apiMember.role === "security" ? "Security" :
          apiMember.role === "audit" ? "Audit" : "User",
    status: "Active", // Team members are always active
    dateAdded: new Date(apiMember.joinedAt || apiMember.invitedAt).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    }),
  };
};



export const useUsers = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [pendingInvitations, setPendingInvitations] = useState<number>(0);
  const [pendingInvitationsList, setPendingInvitationsList] = useState<ApiInvitation[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Refs for managing polling
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const isPollingActiveRef = useRef<boolean>(false);

  // Get user role access
  const { userRole, isAdmin } = useRoleAccess();


  // Check if there are any team members first (lightweight check)
  const checkForTeamMembers = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      console.log("Checking for existing team members...", { userRole, isAdmin });

      // For non-admin users, try to fetch team members directly (read-only)
      if (!isAdmin) {
        console.log("Non-admin user - attempting read-only team member fetch");
        try {
          await fetchTeamMembers();
          console.log("Non-admin user successfully fetched team members");
        } catch (error: unknown) {
          const apiError = error as ApiError;
          console.log("Non-admin user team member fetch failed:", apiError.response?.status);

          // Only show empty list if it's a clear permission denial (403)
          // For other errors (404, network issues), we'll still try to show what we can
          if (apiError.response?.status === 403) {
            console.log("Access denied (403) - user doesn't have permission to view team members");
            setUsers([]);
          } else {
            console.log("API error but not permission-related - keeping any existing data");
            // Don't clear existing users data for non-permission errors
          }
        }

        // Non-admins can't see invitations regardless of team member access
        setPendingInvitations(0);
        setPendingInvitationsList([]);
        setLoading(false);
        return;
      }

      // Admin users get full access - try to get invitation stats first
      const statsResponse = await userService.getInvitationStats();

      if (statsResponse.result) {
        const totalInvitations = statsResponse.result.total || 0;
        const acceptedInvitations = statsResponse.result.accepted || 0;

        console.log("Invitation stats:", statsResponse.result);

        // If there are accepted invitations or pending ones, fetch full data
        if (totalInvitations > 0 || acceptedInvitations > 0) {
          console.log("Found existing invitations, fetching full team data...");
          await fetchTeamMembers();
          setPendingInvitations(statsResponse.result.pending || 0);

          // Fetch pending invitations list if there are any
          if (statsResponse.result.pending > 0) {
            await fetchPendingInvitations();
          } else {
            setPendingInvitationsList([]);
          }
        } else {
          console.log("No team members found, showing admin only");
          setUsers([]);
          setPendingInvitations(0);
          setPendingInvitationsList([]);
        }
      } else {
        console.log("No stats available, showing admin only");
        setUsers([]);
        setPendingInvitations(0);
        setPendingInvitationsList([]);
      }


    } catch (err: unknown) {
      const error = err as ApiError;
      console.error("Error checking for team members:", error);

      // If API is not available, just show admin only
      setUsers([]);
      setPendingInvitations(0);
      setPendingInvitationsList([]);


      // Only show error if it's not a 404 (API not available) or 403 (permission denied)
      if (error.response?.status !== 404 && error.response?.status !== 403) {
        const errorMessage = error.response?.data?.message || error.message || "Failed to check team members";
        setError(errorMessage);
        showDetailedErrorToast(errorMessage, error.response?.data?.errors || []);
      } else if (error.response?.status === 404) {
        console.log("Team management API not available (404) - showing admin only");
      } else if (error.response?.status === 403) {
        console.log("Access denied (403) - user doesn't have permission for team management");
      }
    } finally {
      setLoading(false);
    }
  }, [userRole, isAdmin]);

  // Fetch team members (active users) - accessible to all authenticated users
  const fetchTeamMembers = async () => {
    try {
      console.log("Fetching team members...");
      const response = await userService.getTeamMembers();

      if (response.result && Array.isArray(response.result)) {
        const transformedUsers = response.result.map(transformApiTeamMember);
        setUsers(transformedUsers);
        console.log("Team members fetched successfully:", transformedUsers.length, "users");
      } else {
        setUsers([]);
        console.log("No team members found");
      }
    } catch (err: unknown) {
      const error = err as ApiError;
      console.error("Error fetching team members:", error);

      // For non-admin users, don't show error toasts for permission issues
      // They should still be able to see the page, just without team member data
      if (error.response?.status === 403) {
        console.log("Access denied (403) - user doesn't have permission to fetch team members");
        setUsers([]);
        // Don't show error toast for permission issues - this is expected for some users
      } else if (error.response?.status === 404) {
        console.log("Team members endpoint not found (404) - API may not be available");
        setUsers([]);
        // Don't show error toast for 404 - this is expected if API is not implemented
      } else {
        // Only show error toasts for unexpected errors (network issues, server errors, etc.)
        setUsers([]);
        const errorMessage = error.response?.data?.message || error.message || "Failed to fetch team members";
        setError(errorMessage);
        showDetailedErrorToast(errorMessage, error.response?.data?.errors || []);
      }
    }
  };

  // Fetch pending invitations list
  const fetchPendingInvitations = async () => {
    try {
      console.log("Fetching pending invitations...");
      const response = await userService.getInvitations("pending");
      console.log("Raw invitations response:", response);

      if (response.result && Array.isArray(response.result)) {
        console.log("Setting pending invitations list:", response.result);
        setPendingInvitationsList(response.result);
        console.log("Pending invitations fetched successfully:", response.result.length, "invitations");
      } else {
        setPendingInvitationsList([]);
        console.log("No pending invitations found");
      }
    } catch (err: unknown) {
      const error = err as ApiError;
      console.error("Error fetching pending invitations:", error);
      setPendingInvitationsList([]);

      // Only show error if it's not a 404 (API not available) or 403 (permission denied)
      if (error.response?.status !== 404 && error.response?.status !== 403) {
        console.log("Pending invitations API error - unexpected status:", error.response?.status);
      } else if (error.response?.status === 404) {
        console.log("Pending invitations API not available - this is expected if not implemented yet");
      } else if (error.response?.status === 403) {
        console.log("Access denied (403) - user doesn't have permission to fetch pending invitations");
      }
    }
  };

  // Invite new user
  const inviteUser = async (userData: {
    name: string;
    email: string;
    role: "Admin" | "User" | "Security" | "Audit";
    organizationName: string;
  }): Promise<boolean> => {
    try {
      setLoading(true);
      setError(null);

      // Store the name in cookie for later use when user accepts invitation
      storeInvitedNameInCookie(userData.email, userData.name);

      const inviteData: InviteUserData = {
        email: userData.email,
        role: userData.role.toLowerCase() as "user" | "admin" | "security" | "audit",
        organizationName: userData.organizationName,
        message: `Welcome to our security team! You've been invited to join as a ${userData.role}.`,
        name: userData.name,
      };

      console.log("Attempting to invite user:", inviteData);
      const response = await userService.inviteUser(inviteData);

      if (response.message) {
        toast.success("Invitation sent successfully!");
        // Refresh data to get latest state
        await checkForTeamMembers();
        return true;
      }
      return false;
    } catch (err: unknown) {
      const error = err as ApiError;
      console.error("Error inviting user:", error);

      // Check if this is a 404 (API not available)
      if (error.response?.status === 404) {
        console.log("API endpoint not found - this is expected if the backend team hasn't implemented the user management endpoints yet");

        // For now, show a user-friendly message with detailed error info
        showDetailedErrorToast(
          "User invitation feature is not yet available. Please contact your administrator.",
          ["API endpoint not found", "Backend may not have implemented user management endpoints yet"]
        );

        // Don't show the detailed error toast for 404s as it's expected
        return false;
      }

      const errorMessage = error.response?.data?.message || error.message || "Failed to send invitation";
      setError(errorMessage);
      showDetailedErrorToast(errorMessage, error.response?.data?.errors || []);
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Update user
  const updateUser = async (userId: string, userData: {
    name: string;
    email: string;
    role: "Admin" | "User" | "Security" | "Audit";
  }): Promise<boolean> => {
    try {
      setLoading(true);
      setError(null);

      // Convert role to lowercase for API
      const updateData = {
        name: userData.name,
        email: userData.email,
        role: userData.role.toLowerCase()
      };

      // For debugging: try without role first to see if the endpoint works
      const updateDataWithoutRole = {
        name: userData.name,
        email: userData.email
      };

      // Check if this is a temporary ID (which means we can't update this user)
      if (userId.includes('temp-')) {
        throw new Error("Cannot update user: Invalid user ID. This user may not be properly synced with the backend.");
      }

      console.log("Updating user with current role context:", {
        userId,
        updateData,
        updateDataWithoutRole,
        currentUserRole: Cookies.get("userRole")
      });

      // First try with role, if it fails with permission error, try without role
      let response;
      try {
        response = await userService.updateUser(userId, updateData);
      } catch (roleError: unknown) {
        const error = roleError as ApiError;
        if (error.response?.status === 403 &&
            error.response?.data?.message?.toLowerCase().includes("cannot change user roles")) {
          console.log("Role change forbidden, trying to update name and email only...");
          showDetailedErrorToast(
            "Role changes are not permitted. Updating name and email only.",
            ["User role updates are restricted by backend policy", "Only name and email will be updated"]
          );
          response = await userService.updateUser(userId, updateDataWithoutRole);
        } else {
          throw error;
        }
      }

      if (response.message) {
        toast.success("User updated successfully!");
        // Refresh data to get latest state
        await checkForTeamMembers();
        return true;
      }
      return false;
    } catch (err: unknown) {
      const error = err as ApiError;
      console.error("Error updating user:", error);
      console.error("Error status:", error.response?.status);
      console.error("Error data:", error.response?.data);

      // Handle specific permission errors
      if (error.response?.status === 403) {
        const message = error.response?.data?.message || "Permission denied";
        if (message.toLowerCase().includes("cannot change user roles")) {
          showDetailedErrorToast(
            "You don't have permission to change user roles. Please contact your administrator.",
            ["Permission denied for role changes", "Contact admin for role modification requests", `Server response: ${message}`]
          );
          setError("Insufficient permissions to change user roles");
          return false;
        }
      }

      const errorMessage = error.response?.data?.message || error.message || "Failed to update user";
      setError(errorMessage);
      showDetailedErrorToast(errorMessage, error.response?.data?.errors || []);
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Remove team member
  const removeUser = async (userId: string): Promise<boolean> => {
    try {
      setLoading(true);
      setError(null);

      const response = await userService.removeTeamMember(userId);
      
      if (response.message) {
        toast.success("User removed successfully!");
        // Refresh data to get latest state
        await checkForTeamMembers();
        return true;
      }
      return false;
    } catch (err: unknown) {
      const error = err as ApiError;
      console.error("Error removing user:", error);
      const errorMessage = error.response?.data?.message || error.message || "Failed to remove user";
      setError(errorMessage);
      showDetailedErrorToast(errorMessage, error.response?.data?.errors || []);
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Revoke invitation
  const revokeInvitation = async (invitationId: string): Promise<boolean> => {
    try {
      setLoading(true);
      setError(null);

      const response = await userService.revokeInvitation(invitationId);
      
      if (response.message) {
        toast.success("Invitation revoked successfully!");
        // Refresh data to get latest state
        await checkForTeamMembers();
        return true;
      }
      return false;
    } catch (err: unknown) {
      const error = err as ApiError;
      console.error("Error revoking invitation:", error);
      const errorMessage = error.response?.data?.message || error.message || "Failed to revoke invitation";
      setError(errorMessage);
      showDetailedErrorToast(errorMessage, error.response?.data?.errors || []);
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Resend invitation
  const resendInvitation = async (invitationId: string, message?: string): Promise<boolean> => {
    try {
      setLoading(true);
      setError(null);

      const response = await userService.resendInvitation(invitationId, message);
      
      if (response.message) {
        toast.success("Invitation resent successfully!");
        return true;
      }
      return false;
    } catch (err: unknown) {
      const error = err as ApiError;
      console.error("Error resending invitation:", error);
      const errorMessage = error.response?.data?.message || error.message || "Failed to resend invitation";
      setError(errorMessage);
      showDetailedErrorToast(errorMessage, error.response?.data?.errors || []);
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Start polling for updates
  const startPolling = useCallback(() => {
    if (pollingIntervalRef.current) return;

    console.log("Starting auto-refresh polling...");
    isPollingActiveRef.current = true;

    pollingIntervalRef.current = setInterval(() => {
      if (isPollingActiveRef.current && !document.hidden) {
        console.log("Auto-refreshing user data...");
        checkForTeamMembers();
      }
    }, 30000); // Poll every 30 seconds
  }, [checkForTeamMembers]);

  // Stop polling
  const stopPolling = useCallback(() => {
    if (pollingIntervalRef.current) {
      console.log("Stopping auto-refresh polling...");
      clearInterval(pollingIntervalRef.current);
      pollingIntervalRef.current = null;
      isPollingActiveRef.current = false;
    }
  }, []);

  // Handle page visibility changes
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden) {
        console.log("Page hidden, pausing auto-refresh...");
        isPollingActiveRef.current = false;
      } else {
        console.log("Page visible, resuming auto-refresh...");
        isPollingActiveRef.current = true;
        // Immediately refresh when page becomes visible
        checkForTeamMembers();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [checkForTeamMembers]);

  // Initialize data on mount and start polling
  useEffect(() => {
    checkForTeamMembers();
    startPolling();

    // Cleanup on unmount
    return () => {
      stopPolling();
    };
  }, [checkForTeamMembers, startPolling, stopPolling]);

  return {
    users,
    pendingInvitations,
    pendingInvitationsList,
    loading,
    error,
    inviteUser,
    updateUser,
    removeUser,
    revokeInvitation,
    resendInvitation,
    refreshData: () => {
      checkForTeamMembers();
    },
  };
};
