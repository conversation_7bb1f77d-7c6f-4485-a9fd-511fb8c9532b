"use client";
import React from "react";
import Sidebar from "@/components/dashboard-comps/Sidebar";
import Header from "@/components/dashboard-comps/Header";
import { useSidebar } from "@/hooks/useSidebar";

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { isSidebarOpen, closeSidebar } = useSidebar();

  return (
    <div className="flex h-screen bg-[#121212] text-white">
      <div
        className={`fixed inset-y-0 left-0 z-50 transform transition-transform duration-300 ease-in-out lg:static lg:transform-none ${
          isSidebarOpen ? "translate-x-0" : "-translate-x-full"
        } lg:translate-x-0 lg:w-[19%] md:w-[40%] w-[80%] bg-[#121212]`}
      >
        <Sidebar onClose={closeSidebar} />
      </div>

      <div className="flex-1 flex flex-col lg:ml-0">
        {/* Fixed Header */}
        <div className="fixed top-0 right-0 left-0 lg:left-[19%] z-30 bg-[#121212] p-6">
          <Header
            onMenuClick={useSidebar().toggleSidebar}
            isSidebarOpen={isSidebarOpen}
          />
        </div>

        {/* Main Content with top padding to account for fixed header */}
        <div className="flex-1 overflow-auto pt-[100px] sm:pt-[120px] p-6">
          {children}
        </div>
      </div>

      {isSidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={closeSidebar}
        />
      )}
    </div>
  );
}
